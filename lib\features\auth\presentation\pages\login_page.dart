library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sasthra/core/theme/app_theme.dart';
import '../widgets/animated_input_field.dart';
import '../../models/login_model.dart';
import '../../controllers/login_controller.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/models/auth_models.dart';
import '../../../../core/services/auth_service.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  // Brand palette (from existing usage in codebase/logo):
  static const kBrandPrimary = Color(0xFF1E88E5); // Blue
  static const kBrandAccent = Color(0xFF4CAF50); // Green
  static const kBackground = Color(0xFFF6F8FB); // Neutral soft bg
  static const kSurface = Colors.white;

  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameFocus = FocusNode();
  final _passwordFocus = FocusNode();
  bool _isPasswordVisible = false;
  final AuthService _authService = AuthService();

  // MVC Components
  late final LoginModel _model;
  late final LoginController _controller;

  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _shakeController;
  late AnimationController _logoController;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeMVC();
    _initializeAnimations();
    _setupControllerListeners();

    // Status bar for better contrast on brand header
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: kBrandPrimary,
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  void _initializeMVC() {
    _model = LoginModel();
    _controller = LoginController(_model);
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 700),
      vsync: this,
    );
    _slideAnimation = CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 450),
      vsync: this,
    );

    _logoController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController.forward();
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) _fadeController.forward();
    });
  }

  void _setupControllerListeners() {
    _controller.addListener(() {
      if (!mounted) return;
      setState(() {});
      _controller.handleNavigation(context);
      if (_controller.shouldTriggerShakeAnimation) {
        _shakeController.forward().then((_) => _shakeController.reset());
        if (_controller.effectiveErrorMessage != null) {
          _showToast(_controller.effectiveErrorMessage!, isError: true);
        }
      }
    });

    _usernameController.addListener(() {
      _controller.onUsernameChanged(_usernameController.text);
    });

    _passwordController.addListener(() {
      _controller.onPasswordChanged(_passwordController.text);
    });
  }

  void _handlePasswordVisibilityToggle() {
    setState(() => _isPasswordVisible = !_isPasswordVisible);
  }

  void _showToast(String message, {bool isError = false}) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: isError ? Colors.red : kBrandPrimary,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) {
      _showToast("Please fill in all fields correctly", isError: true);
      return;
    }

    HapticFeedback.lightImpact();

    final username = _usernameController.text.trim();
    final password = _passwordController.text;

    AppLogger.userAction('Login attempt', {'username': username});

    // 🔹 Test credentials shortcut
    if (username == "TESTUSER" && password == "test001") {
      AppLogger.info("Test credentials used. Skipping OTP flow.");

      _authService.setAuthenticatedUser(
        UserData(
          id: "test_id",
          username: username,
          role: "student",
        ),
      );

      _showToast("Login successful! Welcome, $username");
      if (mounted) {
        context.go('/dashboard');
      }
      return;
    }

    // Normal login
    final result = await _authService.login(username, password);

    if (!result.success && result.message != null) {
      _showToast(result.message!, isError: true);
      HapticFeedback.heavyImpact();
    } else if (result.success) {
      _showToast("Login successful! Welcome, $username");
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _shakeController.dispose();
    _logoController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _usernameFocus.dispose();
    _passwordFocus.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final headingStyle = GoogleFonts.poppins(
      textStyle: AppTheme.headingLarge.copyWith(
        color: Colors.white,
        fontWeight: FontWeight.w700,
      ),
    );
    final subheadingStyle = GoogleFonts.poppins(
      textStyle: AppTheme.bodyMedium.copyWith(
        color: Colors.white.withOpacity(0.9),
      ),
    );

    return Scaffold(
      backgroundColor: kBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.only(bottom: 24),
          child: Column(
            children: [
              // Brand header (solid color, no gradient)
              _buildHeader(headingStyle, subheadingStyle),
              const SizedBox(height: 16),
              // Login card
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: _buildLoginCard(),
              ),
              const SizedBox(height: 16),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(TextStyle heading, TextStyle subheading) {
    return AnimatedBuilder(
      animation: _logoController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.fromLTRB(20, 28, 20, 28),
          decoration: const BoxDecoration(
            color: kBrandPrimary,
            borderRadius: BorderRadius.vertical(
              bottom: Radius.circular(24),
            ),
          ),
          child: Opacity(
            opacity: _logoController.value,
            child: Transform.scale(
              scale: 0.95 + (_logoController.value * 0.05),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo in a clean circular surface
                  Container(
                    width: 96,
                    height: 96,
                    decoration: const BoxDecoration(
                      color: kSurface,
                      shape: BoxShape.circle,
                    ),
                    alignment: Alignment.center,
                    child: Image.asset(
                      'assets/icons/sasthra_logo.png',
                      width: 120,
                      height: 120,
                      fit: BoxFit.contain,
                      errorBuilder: (_, __, ___) => const Icon(
                        Icons.school,
                        size: 40,
                        color: Color.fromARGB(255, 30, 136, 229),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text('Unlock Knowledge!', style: heading, textAlign: TextAlign.center),
                  const SizedBox(height: 6),
                  Text(
                    'Sign in to continue your learning journey',
                    style: subheading,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginCard() {
    return AnimatedBuilder(
      animation: Listenable.merge([_fadeController, _shakeController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(12 * _shakeController.value, 24 * (1 - _fadeController.value)),
          child: Opacity(
            opacity: _fadeController.value,
            child: Card(
              elevation: 2,
              color: kSurface,
              shadowColor: Colors.black.withOpacity(0.06),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
                side: BorderSide(color: Colors.black.withOpacity(0.06)),
              ),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      // Username
                      _buildSolidInput(
                        focusNode: _usernameFocus,
                        child: AnimatedInputField(
                          controller: _usernameController,
                          focusNode: _usernameFocus,
                          label: 'Learn ID',
                          hint: 'Enter your learning ID',
                          prefixIconData: Icons.school,
                          textInputAction: TextInputAction.next,
                          onFieldSubmitted: (_) => _passwordFocus.requestFocus(),
                          validator: (value) => _controller.usernameError,
                          cursorColor: kBrandPrimary,
                          decoration: _inputDecoration(
                            label: 'Learn ID',
                            hint: 'Enter your learning ID',
                            icon: Icons.school,
                            isFocused: _usernameFocus.hasFocus,
                          ),
                        ),
                      ),
                      const SizedBox(height: 14),

                      // Password
                      _buildSolidInput(
                        focusNode: _passwordFocus,
                        child: AnimatedInputField(
                          controller: _passwordController,
                          focusNode: _passwordFocus,
                          label: 'Knowledge Key',
                          hint: 'Enter your password',
                          prefixIconData: Icons.vpn_key,
                          obscureText: !_isPasswordVisible,
                          textInputAction: TextInputAction.done,
                          onFieldSubmitted: (_) => _handleLogin(),
                          validator: (value) => _controller.passwordError,
                          cursorColor: kBrandPrimary,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _isPasswordVisible ? Icons.visibility : Icons.visibility_off,
                              color: _passwordFocus.hasFocus ? kBrandPrimary : AppTheme.textSecondary,
                            ),
                            onPressed: _handlePasswordVisibilityToggle,
                            tooltip: _isPasswordVisible ? 'Hide password' : 'Show password',
                          ),
                          decoration: _inputDecoration(
                            label: 'Knowledge Key',
                            hint: 'Enter your password',
                            icon: Icons.vpn_key,
                            isFocused: _passwordFocus.hasFocus,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Sign In Button (solid brand color, no gradient)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _controller.shouldShowLoading ? null : _handleLogin,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: kBrandPrimary,
                            disabledBackgroundColor: kBrandPrimary.withOpacity(0.6),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 0,
                          ),
                          child: _controller.shouldShowLoading
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: const [
                                    SizedBox(
                                      height: 18,
                                      width: 18,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                      ),
                                    ),
                                    SizedBox(width: 10),
                                    Text('Signing In...'),
                                  ],
                                )
                              : Text(
                                  'Sign In to Learn',
                                  style: GoogleFonts.poppins(
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(height: 8),

                      // Secondary links
                      Align(
                        alignment: Alignment.center,
                        child: TextButton(
                          onPressed: () => context.go("/auth/forgot-password"),
                          style: TextButton.styleFrom(
                            foregroundColor: kBrandPrimary,
                          ),
                          
                          child: Text(
                            'Forgot Password?',
                            style: GoogleFonts.poppins(
                              textStyle: AppTheme.bodyMedium.copyWith(
                                decorationColor: kBrandPrimary,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Wraps inputs in a subtle container to enlarge tap area and provide soft focus accent
  Widget _buildSolidInput({required FocusNode focusNode, required Widget child}) {
    final isFocused = focusNode.hasFocus;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 180),
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: isFocused ? kBrandAccent.withOpacity(0.06) : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: child,
    );
  }

  InputDecoration _inputDecoration({
    required String label,
    required String hint,
    required IconData icon,
    required bool isFocused,
  }) {
    return InputDecoration(
      labelText: label,
      hintText: hint,
      filled: true,
      fillColor: const Color(0xFFF9FAFB),
      contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
      prefixIcon: Icon(icon, color: isFocused ? kBrandPrimary : AppTheme.textSecondary),
      labelStyle: GoogleFonts.poppins(
        textStyle: TextStyle(
          color: isFocused ? kBrandPrimary : AppTheme.textSecondary,
          fontWeight: FontWeight.w500,
        ),
      ),
      hintStyle: GoogleFonts.poppins(
        textStyle: TextStyle(color: AppTheme.textSecondary.withOpacity(0.6)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Colors.black.withOpacity(0.08), width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: kBrandPrimary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.red, width: 2),
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Text(
        'Empowering Education with Tech © 2025 Sasthra. All rights reserved.',
        textAlign: TextAlign.center,
        style: GoogleFonts.poppins(
          textStyle: AppTheme.bodySmall.copyWith(
            color: AppTheme.textTertiary,
          ),
        ),
      ),
    );
  }
}