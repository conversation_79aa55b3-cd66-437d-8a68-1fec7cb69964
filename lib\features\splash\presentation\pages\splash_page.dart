// /// splash_page.dart - Application Splash Screen
// library;

// import 'package:flutter/material.dart';
// import 'package:flutter_animate/flutter_animate.dart';
// import 'package:go_router/go_router.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../../../core/services/auth_service.dart';
// import '../../../../core/models/auth_models.dart';
// import '../../../../core/theme/app_theme.dart';
// import '../../../../core/utils/logger.dart';

// class SplashPage extends StatefulWidget {
//   const SplashPage({super.key});

//   @override
//   State<SplashPage> createState() => _SplashPageState();
// }

// class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
//   late AnimationController _logoController;
//   late AnimationController _textController;
//   final AuthService _authService = AuthService();
//   bool _hasNavigated = false;

//   @override
//   void initState() {
//     super.initState();
//     _initializeAnimations();
//     _initializeApp();
//   }

//   void _initializeAnimations() {
//     _logoController = AnimationController(
//       duration: const Duration(milliseconds: 1500),
//       vsync: this,
//     )..forward();

//     _textController = AnimationController(
//       duration: const Duration(milliseconds: 1000),
//       vsync: this,
//     )..forward();
//   }

//   Future<void> _initializeApp() async {
//    try {
//       AppLogger.info('Initializing app from splash screen');
//        // Start logo animation
//       _logoController.forward();

//       // Wait a bit then start text animation
//       await Future.delayed(const Duration(milliseconds: 500));
//       _textController.forward();

//        await Future.delayed(const Duration(milliseconds: 2000));
//        await _authService.initialize();
//         await Future.delayed(const Duration(milliseconds: 2000));
//       // Navigate based on auth state
//       if (mounted) {
//         _navigateBasedOnAuthState();

//        }
//        }catch (e) {
//       AppLogger.error('App initialization failed: $e');
//       if (mounted) {
//         context.go('/auth/login');
//       }
//       }
//   }

//   void _navigateBasedOnAuthState() {
//     final authState = _authService.state;
//     AppLogger.info('Navigating from splash, auth state: ${authState.name}');

//     switch (authState) {
//       case AuthState.authenticated:
//         context.go('/dashboard');
//         break;
//       case AuthState.otpRequired:
//         context.go('/auth/otp');
//         break;
//       case AuthState.unauthenticated:
//       case AuthState.error:
//       default:
//         context.go('/auth/login');
//         break;
//     }
//   }

//   @override
//   void dispose() {
//     _logoController.dispose();
//     _textController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: AppTheme.primaryColor,
//       body: Container(
//         decoration: const BoxDecoration(
//           gradient: AppTheme.primaryGradient,
//         ),
//         child: SafeArea(
//           child: Column(
//             children: [
//               Expanded(
//                 child: Center(
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: [
//                       AnimatedBuilder(
//                         animation: _logoController,
//                         builder: (context, child) {
//                           return Transform.scale(
//                             scale: _logoController.value,
//                             child: Opacity(
//                               opacity: _logoController.value,
//                               child: Container(
//                                 width: 120,
//                                 height: 120,
//                                 decoration: BoxDecoration(
//                                   color: Colors.white,
//                                   borderRadius: BorderRadius.circular(30),
//                                   boxShadow: [
//                                     BoxShadow(
//                                       color: Colors.black.withOpacity(0.2),
//                                       blurRadius: 20,
//                                       offset: const Offset(0, 10),
//                                     ),
//                                   ],
//                                 ),
//                                 child: const Icon(
//                                   Icons.school,
//                                   size: 60,
//                                   color: AppTheme.primaryColor,
//                                 ),
//                               ),
//                             ),
//                           );
//                         },
//                       ),
//                       const SizedBox(height: 32),
//                       AnimatedBuilder(
//                         animation: _textController,
//                         builder: (context, child) {
//                           return Transform.translate(
//                             offset: Offset(0, 50 * (1 - _textController.value)),
//                             child: Opacity(
//                               opacity: _textController.value,
//                               child: Column(
//                                 children: [
//                                   Text(
//                                     'SASTHRA',
//                                     style: AppTheme.headingLarge.copyWith(
//                                       color: Colors.white,
//                                       fontSize: 36,
//                                       fontWeight: FontWeight.bold,
//                                       letterSpacing: 2,
//                                     ),
//                                   ),
//                                   const SizedBox(height: 8),
//                                   Text(
//                                     'JEE • NEET • Excellence',
//                                     style: AppTheme.bodyLarge.copyWith(
//                                       color: Colors.white.withOpacity(0.9),
//                                       fontSize: 16,
//                                       letterSpacing: 1,
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           );
//                         },
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//               Padding(
//                 padding: const EdgeInsets.only(bottom: 50),
//                 child: Column(
//                   children: [
//                     const SizedBox(
//                       width: 30,
//                       height: 30,
//                       child: CircularProgressIndicator(
//                         valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                         strokeWidth: 3,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     Text(
//                       'Initializing...',
//                       style: AppTheme.bodyMedium.copyWith(
//                         color: Colors.white.withOpacity(0.8),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/models/auth_models.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/logger.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  final AuthService _authService = AuthService();
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
  }

  Future<void> _initializeApp() async {
    try {
      AppLogger.info('Initializing app from splash screen');

      _logoController.forward();
      await Future.delayed(const Duration(milliseconds: 500));
      _textController.forward();

      // ⏱ shorter splash wait
      await Future.delayed(const Duration(milliseconds: 1500));

      await _authService.initialize();

      // small pause before navigating
      await Future.delayed(const Duration(milliseconds: 800));

      if (mounted) {
        _navigateBasedOnAuthState();
      }
    } catch (e) {
      AppLogger.error('App initialization failed: $e');
      if (mounted) {
        context.go('/auth/login');
      }
    }
  }

  void _navigateBasedOnAuthState() {
    if (_hasNavigated) return;
    _hasNavigated = true;

    final authState = _authService.state;
    AppLogger.info('Navigating from splash, auth state: ${authState.name}');

    switch (authState) {
      case AuthState.authenticated:
        context.go('/dashboard');
        break;
      case AuthState.otpRequired:
        context.go('/auth/otp');
        break;
      case AuthState.unauthenticated:
      case AuthState.error:
      default:
        context.go('/auth/login');
        break;
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              flex: 2,
              child: Image.asset(
                'assets/animations/splash.gif', // ✅ your GIF file here
                fit: BoxFit.contain,
                width: double.infinity,
              ),
            ),

            // Bottom Half - Welcome message
            Expanded(
              flex: 1,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    const Text(
                      "Welcome to Sasthra!",
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      "Your smart companion for NEET & JEE preparation.",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    const Spacer(),

                    // 🔹 Replaced button with loading indicator
                    Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const CircularProgressIndicator(
                            color: Color.fromARGB(255, 30, 136, 229),
                            strokeWidth: 3,
                          ),
                          const SizedBox(height: 12),
                          Text(
                            "Preparing your dashboard...",
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
