import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// AppTheme - Application Theme Configuration Class
///
/// English: This class defines the complete visual design system for the Sasthra mobile application.
/// It contains all color schemes, typography, component themes, and styling configurations that
/// ensure consistent UI/UX across the entire application. It supports both light and dark themes
/// with Material Design 3 principles.
///
/// Tanglish: Inga AppTheme class la naama app ku vendiya ella visual design um define pannirukkom.
/// Colors, fonts, button styles, input field styles - ella theme related configurations um
/// inga irukku. Light theme um dark theme um support pannum. Material Design 3 follow pannum.
///
/// Key Features:
/// - Comprehensive color palette with semantic naming
/// - Typography system using Google Fonts (Poppins)
/// - Component-specific themes (buttons, inputs, cards, etc.)
/// - Light and dark theme support
/// - Consistent spacing and border radius values
/// - Role-based color coding support with 7 distinct role colors
/// - Dynamic theme generation for different user roles
/// - Helper methods for role-specific styling
///
/// Role Colors:
/// - Parent: #10E7DC (Turquoise/Cyan)
/// - Counselor: #F4C430 (Golden Yellow)
/// - Trainee: #F59E0B (Amber/Orange)
/// - Student: #2563EB (Blue)
/// - Director: #7D1E1C (Dark Red/Maroon)
/// - Teacher: #000080 (Navy Blue)
/// - Mentor: #7C007C (Purple/Magenta)
///
/// Usage:
/// - Basic theme: MaterialApp.router(theme: AppTheme.lightTheme, darkTheme: AppTheme.darkTheme)
/// - Role-specific theme: MaterialApp.router(theme: AppTheme.getRoleTheme('parent'))
/// - Get role color: AppTheme.getRoleColor('student')
/// - Get role gradient: AppTheme.getRoleGradient('director')
class AppTheme {
  // Color Palette
  static const Color primaryColor = Color(0xFF6366F1); // Indigo
  static const Color primaryVariant = Color(0xFF4F46E5);
  static const Color sasthraBlue = Color(0xFF000080);
  static const Color secondaryColor = Color(0xFF10B981); // Emerald
  static const Color secondaryVariant = Color(0xFF059669);
  
  static const Color backgroundColor = Color(0xFFF8FAFC);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFEF4444);
  static const Color warningColor = Color(0xFFF59E0B);
  static const Color successColor = Color(0xFF10B981);
  
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textTertiary = Color(0xFF9CA3AF);
  static const Color textOtp = Color(0xFFF5F5DC); // Light cream (beige)
  
  static const Color borderColor = Color(0xFFE5E7EB);
  static const Color dividerColor = Color(0xFFF3F4F6);

  // Role-Specific Color Palette
  static const Color parentColor = Color(0xFF10E7DC); // Turquoise/Cyan
  static const Color counselorColor = Color(0xFFF4C430); // Golden Yellow
  static const Color traineeColor = Color(0xFFF59E0B); // Amber/Orange
  static const Color studentColor = Color(0xFF2563EB); // Blue
  static const Color directorColor = Color(0xFF7D1E1C); // Dark Red/Maroon
  static const Color teacherColor = Color(0xFF000080); // Navy Blue
  static const Color mentorColor = Color(0xFF7C007C); // Purple/Magenta

  // Role-Specific Color Variants (lighter shades for containers, backgrounds)
  static const Color parentColorLight = Color(0xFF7FFDF7); // Light turquoise
  static const Color counselorColorLight = Color(0xFFFAE68A); // Light golden
  static const Color traineeColorLight = Color(0xFFFDE68A); // Light amber
  static const Color studentColorLight = Color(0xFF93C5FD); // Light blue
  static const Color directorColorLight = Color(0xFFDC2626); // Light red
  static const Color teacherColorLight = Color(0xFF3B82F6); // Light navy
  static const Color mentorColorLight = Color(0xFFC084FC); // Light purple

  // Dark Theme Colors
  static const Color darkBackgroundColor = Color(0xFF0F172A);
  static const Color darkSurfaceColor = Color(0xFF1E293B);
  static const Color darkTextPrimary = Color(0xFFF1F5F9);
  static const Color darkTextSecondary = Color(0xFFCBD5E1);
  static const Color darkBorderColor = Color(0xFF334155);

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, primaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryColor, secondaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Role-Specific Gradients
  static const LinearGradient parentGradient = LinearGradient(
    colors: [parentColor, parentColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient counselorGradient = LinearGradient(
    colors: [counselorColor, counselorColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient traineeGradient = LinearGradient(
    colors: [traineeColor, traineeColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient studentGradient = LinearGradient(
    colors: [studentColor, studentColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient directorGradient = LinearGradient(
    colors: [directorColor, directorColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient teacherGradient = LinearGradient(
    colors: [teacherColor, teacherColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient mentorGradient = LinearGradient(
    colors: [mentorColor, mentorColorLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Text Styles
  static TextStyle get headingLarge => GoogleFonts.poppins(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: textPrimary,
    height: 1.2,
  );

  static TextStyle get headingMedium => GoogleFonts.poppins(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    height: 1.3,
  );

  static TextStyle get headingSmall => GoogleFonts.poppins(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    height: 1.4,
  );

  static TextStyle get bodyLarge => GoogleFonts.poppins(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: textPrimary,
    height: 1.5,
  );

  static TextStyle get bodyMedium => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: textPrimary,
    height: 1.5,
  );

  static TextStyle get bodySmall => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: textSecondary,
    height: 1.4,
  );

  static TextStyle get labelLarge => GoogleFonts.poppins(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    height: 1.4,
  );

  static TextStyle get labelMedium => GoogleFonts.poppins(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: textSecondary,
    height: 1.4,
  );

  // Role-Based Color Helper Methods
  /// Get primary color for a specific role
  static Color getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'parent':
        return parentColor;
      case 'counselor':
      case 'center_counselor':
        return counselorColor;
      case 'trainee':
        return traineeColor;
      case 'student':
        return studentColor;
      case 'director':
        return directorColor;
      case 'teacher':
      case 'faculty':
      case 'kota_teacher':
        return teacherColor;
      case 'mentor':
      case 'mendor':
        return mentorColor;
      default:
        return primaryColor; // Fallback to default primary color
    }
  }

  /// Get light variant color for a specific role (for backgrounds, containers)
  static Color getRoleColorLight(String role) {
    switch (role.toLowerCase()) {
      case 'parent':
        return parentColorLight;
      case 'counselor':
      case 'center_counselor':
        return counselorColorLight;
      case 'trainee':
        return traineeColorLight;
      case 'student':
        return studentColorLight;
      case 'director':
        return directorColorLight;
      case 'teacher':
      case 'faculty':
      case 'kota_teacher':
        return teacherColorLight;
      case 'mentor':
      case 'mendor':
        return mentorColorLight;
      default:
        return const Color(0xFFEEF2FF); // Fallback to default light color
    }
  }

  /// Get role-specific gradient
  static LinearGradient getRoleGradient(String role) {
    final primaryColor = getRoleColor(role);
    final lightColor = getRoleColorLight(role);

    return LinearGradient(
      colors: [primaryColor, lightColor],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// Get role name with proper formatting
  static String getRoleDisplayName(String role) {
    switch (role.toLowerCase()) {
      case 'parent':
        return 'Parent';
      case 'counselor':
      case 'center_counselor':
        return 'Counselor';
      case 'trainee':
        return 'Trainee';
      case 'student':
        return 'Student';
      case 'director':
        return 'Director';
      case 'teacher':
      case 'faculty':
      case 'kota_teacher':
        return 'Teacher';
      case 'mentor':
      case 'mendor':
        return 'Mentor';
      default:
        return 'User';
    }
  }

  /// Create a role-specific theme based on the base light theme
  static ThemeData getRoleTheme(String role) {
    final roleColor = getRoleColor(role);
    final roleLightColor = getRoleColorLight(role);

    return lightTheme.copyWith(
      colorScheme: lightTheme.colorScheme.copyWith(
        primary: roleColor,
        primaryContainer: roleLightColor,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: roleColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: roleColor,
          side: BorderSide(color: roleColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: roleColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      inputDecorationTheme: lightTheme.inputDecorationTheme.copyWith(
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: roleColor, width: 2),
        ),
      ),
      bottomNavigationBarTheme: lightTheme.bottomNavigationBarTheme.copyWith(
        selectedItemColor: roleColor,
      ),
    );
  }

  // Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        primaryContainer: Color(0xFFEEF2FF),
        secondary: secondaryColor,
        secondaryContainer: Color(0xFFD1FAE5),
        surface: surfaceColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimary,
        onError: Colors.white,
      ),
      
      textTheme: TextTheme(
        headlineLarge: headingLarge,
        headlineMedium: headingMedium,
        headlineSmall: headingSmall,
        bodyLarge: bodyLarge,
        bodyMedium: bodyMedium,
        bodySmall: bodySmall,
        labelLarge: labelLarge,
        labelMedium: labelMedium,
      ),

      appBarTheme: AppBarTheme(
        backgroundColor: surfaceColor,
        foregroundColor: textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: headingSmall,
        iconTheme: const IconThemeData(color: textPrimary),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          side: const BorderSide(color: primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: errorColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: bodyMedium.copyWith(color: textSecondary),
        hintStyle: bodyMedium.copyWith(color: textTertiary),
      ),

      cardTheme: CardThemeData(
        color: surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.all(8),
      ),

      drawerTheme: const DrawerThemeData(
        backgroundColor: surfaceColor,
        elevation: 8,
      ),

      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: surfaceColor,
        selectedItemColor: primaryColor,
        unselectedItemColor: textSecondary,
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),

      dividerTheme: const DividerThemeData(
        color: dividerColor,
        thickness: 1,
      ),
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: const ColorScheme.dark(
        primary: primaryColor,
        primaryContainer: Color(0xFF312E81),
        secondary: secondaryColor,
        secondaryContainer: Color(0xFF064E3B),
        surface: darkSurfaceColor,
        error: errorColor,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: darkTextPrimary,
        onError: Colors.white,
      ),
      
      textTheme: TextTheme(
        headlineLarge: headingLarge.copyWith(color: darkTextPrimary),
        headlineMedium: headingMedium.copyWith(color: darkTextPrimary),
        headlineSmall: headingSmall.copyWith(color: darkTextPrimary),
        bodyLarge: bodyLarge.copyWith(color: darkTextPrimary),
        bodyMedium: bodyMedium.copyWith(color: darkTextPrimary),
        bodySmall: bodySmall.copyWith(color: darkTextSecondary),
        labelLarge: labelLarge.copyWith(color: darkTextPrimary),
        labelMedium: labelMedium.copyWith(color: darkTextSecondary),
      ),

      appBarTheme: AppBarTheme(
        backgroundColor: darkSurfaceColor,
        foregroundColor: darkTextPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: headingSmall.copyWith(color: darkTextPrimary),
        iconTheme: const IconThemeData(color: darkTextPrimary),
      ),

      cardTheme: CardThemeData(
        color: darkSurfaceColor,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.all(8),
      ),

      drawerTheme: const DrawerThemeData(
        backgroundColor: darkSurfaceColor,
        elevation: 8,
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: darkSurfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkBorderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: darkBorderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        labelStyle: bodyMedium.copyWith(color: darkTextSecondary),
        hintStyle: bodyMedium.copyWith(color: darkTextSecondary),
      ),
    );
  }
}
