import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdfx/pdfx.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart' show debugPrint;
import 'dart:convert';
import 'dart:convert' show jsonEncode, jsonDecode;
import 'dart:async';
import '../../../core/config/app_config.dart';
import 'tts_service.dart';
import 'topics_page.dart';

class PdfViewerPage extends StatefulWidget {
  final String processorUrl;
  final String subjectName;
  final String processSelectorId;
  final String languageId;

  const PdfViewerPage({
    Key? key,
    required this.processorUrl,
    required this.subjectName,
    required this.processSelectorId,
    required this.languageId,
  }) : super(key: key);

  @override
  State<PdfViewerPage> createState() => _PdfViewerPageState();
}

class _PdfViewerPageState extends State<PdfViewerPage> {
  static const String baseUrl = "https://testing.sasthra.in";
  PdfController? _pdfController;
  int _totalPages = 0;
  int _currentPage = 1;
  bool _isPdfLoading = true;
  Map<String, String> _slideTranslations = {};
  String? _error;
  late TTSService _ttsService;
  String? _currentlyPlayingSlideKey;

  // Timer
  late Timer _timer;
  int _elapsedSeconds = 0;

  // Fullscreen toggle
  bool _isFullscreen = false;

  @override
  void initState() {
    super.initState();
    _ttsService = TTSService();

    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    _loadPdf();
    _fetchSlideContent();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _elapsedSeconds++;
      });
    });
  }

  String get _formattedTime {
    final hours = (_elapsedSeconds ~/ 3600).toString().padLeft(2, '0');
    final minutes = ((_elapsedSeconds % 3600) ~/ 60).toString().padLeft(2, '0');
    final seconds = (_elapsedSeconds % 60).toString().padLeft(2, '0');
    return "$hours : $minutes : $seconds";
  }

  Future<void> _loadPdf() async {
    try {
      final response = await http.get(Uri.parse(widget.processorUrl));
      if (response.statusCode == 200) {
        final doc = await PdfDocument.openData(response.bodyBytes);
        setState(() {
          _pdfController = PdfController(
            document: Future.value(doc),
            initialPage: 1,
          );
          _totalPages = doc.pagesCount;
          _isPdfLoading = false;
        });
      } else {
        setState(() {
          _isPdfLoading = false;
          _error = "Failed to load PDF: ${response.statusCode}";
        });
      }
    } catch (e) {
      setState(() {
        _isPdfLoading = false;
        _error = "Error loading PDF: $e";
      });
    }
  }

  Future<void> _fetchSlideContent() async {
    try {
      final endpoint = _getEndpoint(widget.subjectName);
      if (endpoint.isEmpty) throw Exception("Invalid subject: ${widget.subjectName}");

      final requestBody = jsonEncode({
        "process_selector_id": widget.processSelectorId,
        "language": widget.languageId,
      });

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          "Content-Type": "application/json",
          ...AppConfig.defaultHeaders,
        },
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final Map<String, String> slideTranslations = {};
        data.forEach((key, value) {
          slideTranslations[key] = value is Map && value['content'] != null
              ? value['content'].toString()
              : value.toString();
        });

        setState(() {
          _slideTranslations = slideTranslations;
        });
      } else {
        setState(() {
          _error = "Failed to fetch slide content: ${response.statusCode}";
        });
      }
    } catch (e) {
      setState(() {
        _error = "Error fetching slide content: $e";
      });
    }
  }

  String _getEndpoint(String subject) {
    final endpoints = {
      "Mathematics": "$baseUrl/classroom/mathapi/explain_slides",
      "Chemistry": "$baseUrl/classroom/chemapi/explain_slides",
      "Physics": "$baseUrl/classroom/physicsapi/explain_slides",
      "Biology": "$baseUrl/classroom/bioapi/explain_slides"
    };
    return endpoints[subject] ?? "";
  }

  Future<void> _playSlideContent(String content, String slideKey) async {
    if (_ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey) {
      await _ttsService.pause();
      setState(() => _currentlyPlayingSlideKey = null);
    } else {
      await _ttsService.play(content, () async {
        setState(() => _currentlyPlayingSlideKey = null);

        if (_currentPage < _totalPages) {
          _pdfController?.jumpToPage(_currentPage + 1);
          setState(() => _currentPage++);
          String nextSlideKey = "slide $_currentPage";
          String nextCaption = _slideTranslations[nextSlideKey] ?? "";
          if (nextCaption.isNotEmpty) _playSlideContent(nextCaption, nextSlideKey);
        }
      });
      setState(() => _currentlyPlayingSlideKey = slideKey);
    }
  }

  Future<void> _stopSlideContent() async {
    await _ttsService.stop();
    setState(() => _currentlyPlayingSlideKey = null);
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  @override
  void dispose() {
    _pdfController?.dispose();
    _ttsService.dispose();
    _timer.cancel();
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String slideKey = "slide $_currentPage";
    String caption = _slideTranslations[slideKey] ?? "No caption";

    return WillPopScope(
      onWillPop: () async {
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => TopicsPage(
              subjectId: widget.processSelectorId,
              subjectName: widget.subjectName,
            ),
          ),
        );
        return false;
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF0D1321),
        body: Column(
          children: [
            // ✅ Show top bar only if not fullscreen
            if (!_isFullscreen)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                color: Colors.white,
                child: Row(
                  children: [
                    const Text(
                      "Learning Viewer",
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    const Spacer(),

                    // Prev
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        minimumSize: const Size(60, 32),
                      ),
                      onPressed: _currentPage > 1
                          ? () {
                              _pdfController?.jumpToPage(_currentPage - 1);
                              setState(() => _currentPage--);
                              _stopSlideContent();
                            }
                          : null,
                      child: const Text("Prev", style: TextStyle(fontSize: 12)),
                    ),
                    const SizedBox(width: 8),

                    Text("$_currentPage / $_totalPages",
                        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                    const SizedBox(width: 8),

                    // Next
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        minimumSize: const Size(60, 32),
                      ),
                      onPressed: _currentPage < _totalPages
                          ? () {
                              _pdfController?.jumpToPage(_currentPage + 1);
                              setState(() => _currentPage++);
                              _stopSlideContent();
                            }
                          : null,
                      child: const Text("Next", style: TextStyle(fontSize: 12)),
                    ),
                    const SizedBox(width: 12),

                    // Play/Pause
                    ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _ttsService.isPlaying &&
                                _currentlyPlayingSlideKey == slideKey
                            ? Colors.red
                            : Colors.green,
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                        minimumSize: const Size(70, 32),
                      ),
                      onPressed: () {
                        if (caption != "No caption") {
                          _playSlideContent(caption, slideKey);
                        }
                      },
                      icon: Icon(
                        _ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey
                            ? Icons.pause
                            : Icons.play_arrow,
                        size: 18,
                        color: Colors.white,
                      ),
                      label: Text(
                        _ttsService.isPlaying && _currentlyPlayingSlideKey == slideKey
                            ? "Pause"
                            : "Play",
                        style: const TextStyle(fontSize: 12, color: Colors.white),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // Break Time (Text button)
                    TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.purple,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      ),
                      onPressed: () {
                        // TODO: Add your break time logic
                      },
                      child: const Text("Break Time", style: TextStyle(fontSize: 14)),
                    ),
                    const SizedBox(width: 8),

                    Text(
                      _formattedTime,
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    ),
                    const SizedBox(width: 16),

                    // Fullscreen toggle
                    IconButton(
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.grey[800],
                        padding: const EdgeInsets.all(6),
                      ),
                      onPressed: _toggleFullscreen,
                      icon: Icon(
                        _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),

            // PDF Viewer
            Expanded(
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(color: Colors.black26, blurRadius: 10, spreadRadius: 2),
                    ],
                  ),
                  child: _error != null
                      ? Center(child: Text(_error!, style: const TextStyle(color: Colors.red)))
                      : _isPdfLoading || _pdfController == null
                          ? const Center(child: CircularProgressIndicator())
                          : ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: PdfView(
                                controller: _pdfController!,
                                scrollDirection: Axis.horizontal,
                                onPageChanged: (page) {
                                  setState(() {
                                    _currentPage = page;
                                    _stopSlideContent();
                                  });
                                },
                              ),
                            ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
